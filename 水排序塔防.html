<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emoji小兵塔防</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#edf6fc',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 全局游戏变量
    let game;
    let columns = [];
    let selectedColumn = null;
    let timer = 160;
    let timerText;
    let titleText;
    let gameStarted = false;
    let gameWon = false;
    let gameScene = null;
    let currentLevel = parseInt(localStorage.getItem('currentLevel')) || 1;
    let levelText;

    const COLUMN_CAPACITY = 4; // 每列最多4个方块
    const CUBE_SIZE = 80; // 适合竖屏的方块大小
    const COLUMN_WIDTH = 110; // 列宽
    const COLUMN_HEIGHT = COLUMN_CAPACITY * CUBE_SIZE + 40;

    // 关卡配置 - 每关有不同的emoji小兵组合
    const LEVEL_CONFIG = {
        1: {
            columns: 6,
            emptyColumns: 2,
            time: 160,

            soldiers: {
                yellow: {
                    emoji: "🤴",
                    name: "国王",
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 },
                    attack: 8,
                    health: 15
                },
                blue: {
                    emoji: "🧙‍♂️",
                    name: "法师",
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
                    attack: 10,
                    health: 12
                }
            }
        },
        2: {
            columns: 6,
            emptyColumns: 2,
            time: 180,

            soldiers: {
                green: {
                    emoji: "🏹",
                    name: "弓箭手",
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
                    attack: 6,
                    health: 10
                },
                red: {
                    emoji: "⚔️",
                    name: "剑士",
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
                    attack: 12,
                    health: 18
                },
                purple: {
                    emoji: "🛡️",
                    name: "盾兵",
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 },
                    attack: 4,
                    health: 25
                }
            }
        },
        3: {
            columns: 8,
            emptyColumns: 2,
            time: 200,

            soldiers: {
                yellow: {
                    emoji: "🤴",
                    name: "国王",
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 },
                    attack: 8,
                    health: 15
                },
                blue: {
                    emoji: "🧙‍♂️",
                    name: "法师",
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
                    attack: 10,
                    health: 12
                },
                green: {
                    emoji: "🏹",
                    name: "弓箭手",
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
                    attack: 6,
                    health: 10
                },
                orange: {
                    emoji: "🔥",
                    name: "火法师",
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 },
                    attack: 11,
                    health: 13
                }
            }
        },
        4: {
            columns: 8,
            emptyColumns: 2,
            time: 220,

            soldiers: {
                red: {
                    emoji: "⚔️",
                    name: "剑士",
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
                    attack: 12,
                    health: 18
                },
                purple: {
                    emoji: "🛡️",
                    name: "盾兵",
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 },
                    attack: 4,
                    health: 25
                },
                cyan: {
                    emoji: "🏊‍♂️",
                    name: "水兵",
                    colors: { main: 0x00BCD4, shadow: 0x0097A7, highlight: 0xB2EBF2 },
                    attack: 7,
                    health: 14
                },
                pink: {
                    emoji: "🌸",
                    name: "花仙",
                    colors: { main: 0xE91E63, shadow: 0xC2185B, highlight: 0xF8BBD9 },
                    attack: 9,
                    health: 13
                }
            }
        },
        5: {
            columns: 10,
            emptyColumns: 2,
            time: 240,

            soldiers: {
                yellow: {
                    emoji: "🤴",
                    name: "国王",
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 },
                    attack: 8,
                    health: 15
                },
                blue: {
                    emoji: "🧙‍♂️",
                    name: "法师",
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
                    attack: 10,
                    health: 12
                },
                green: {
                    emoji: "🏹",
                    name: "弓箭手",
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
                    attack: 6,
                    health: 10
                },
                red: {
                    emoji: "⚔️",
                    name: "剑士",
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
                    attack: 12,
                    health: 18
                },
                orange: {
                    emoji: "🔥",
                    name: "火法师",
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 },
                    attack: 11,
                    health: 13
                }
            }
        },
        6: {
            columns: 10,
            emptyColumns: 2,
            time: 260,

            soldiers: {
                yellow: {
                    emoji: "🤴",
                    name: "国王",
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 },
                    attack: 8,
                    health: 15
                },
                blue: {
                    emoji: "🧙‍♂️",
                    name: "法师",
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
                    attack: 10,
                    health: 12
                },
                green: {
                    emoji: "🏹",
                    name: "弓箭手",
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
                    attack: 6,
                    health: 10
                },
                red: {
                    emoji: "⚔️",
                    name: "剑士",
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
                    attack: 12,
                    health: 18
                },
                purple: {
                    emoji: "🛡️",
                    name: "盾兵",
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 },
                    attack: 4,
                    health: 25
                },
                orange: {
                    emoji: "🔥",
                    name: "火法师",
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 },
                    attack: 11,
                    health: 13
                }
            }
        }
    };

    // 当前关卡的小兵数据（从关卡配置中获取）
    let currentSoldiers = {};
    let currentSoldierNames = [];

    // 防御塔系统
    let defenseTowers = [];
    let battleUnits = []; // 战斗中的小兵单位
    let battleScene = null;

    function preload() {
        // 预加载阶段 - 现在我们动态创建方块，所以这里不需要预先生成纹理
        const levelConfig = LEVEL_CONFIG[currentLevel];

       
       

        
    }

    function create() {
        gameScene = this;

        // 获取当前关卡配置
        const levelConfig = LEVEL_CONFIG[currentLevel] || LEVEL_CONFIG[1];

       

        // 关卡显示背景
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x1dc056, 0.9);
        levelBg.fillRoundedRect(config.width/2 - 70, 80, 140, 60, 12);
        levelBg.lineStyle(2, 0x333333);
        levelBg.strokeRoundedRect(config.width/2 - 70, 80, 140, 60, 12);

        // 关卡显示
        levelText = this.add.text(config.width/2, 110, `LEVEL ${currentLevel}`, {
            fontSize: '30px',
            fontFamily: '"Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#1f783f',
            align: 'center',
            stroke: '#1f783f',
            strokeThickness: 1
        }).setOrigin(0.5);

        // 初始化当前关卡的小兵数据
        currentSoldiers = levelConfig.soldiers;
        currentSoldierNames = Object.keys(currentSoldiers);

        // 创建防御塔
        createDefenseTowers();

      

        // 进度条
        this.progressBar = this.add.graphics();

     
    

        // 初始化关卡
        initializeLevel();

        // 保存战斗场景引用
        battleScene = this;

       

        // 输入处理
        this.input.on('pointerdown', function(pointer) {
            handleInput(pointer);
        });

        // 移除计时器相关代码
    }

    function initializeLevel() {
        // 清除现有列
        columns.forEach(col => col.destroy());
        columns = [];

        // 获取当前关卡配置
        const levelConfig = LEVEL_CONFIG[currentLevel] || LEVEL_CONFIG[1];
        const numColumns = levelConfig.columns;
        const numColors = currentSoldierNames.length; // 使用当前关卡的小兵数量
        const cubesPerColor = 4; // 每种颜色4个方块
        const emptyColumns = levelConfig.emptyColumns;

        // 生成关卡数据
        const levelData = generateRandomLevel(numColumns, numColors, cubesPerColor, emptyColumns);

        // 计算列的位置 - 竖屏布局优化，动态分布
        const totalColumns = levelData.length;
        let columnsPerRow, rows;

        // 根据总列数决定每行列数
        if (totalColumns <= 6) {
            columnsPerRow = 3;
            rows = Math.ceil(totalColumns / columnsPerRow);
        } else if (totalColumns <= 8) {
            columnsPerRow = 4;
            rows = Math.ceil(totalColumns / columnsPerRow);
        } else {
            columnsPerRow = 5;
            rows = Math.ceil(totalColumns / columnsPerRow);
        }

        const totalWidth = columnsPerRow * COLUMN_WIDTH;
        const startX = (config.width - totalWidth) / 2 + COLUMN_WIDTH/2;

        // 计算行的Y位置
        const rowSpacing = 350; // 行间距
        const startY = config.height/2 - (rows - 1) * rowSpacing / 2+350;

        levelData.forEach((cubes, index) => {
            const row = Math.floor(index / columnsPerRow);
            const col = index % columnsPerRow;

            // 如果是最后一行且列数不满，居中显示
            let x;
            const isLastRow = row === rows - 1;
            const columnsInThisRow = isLastRow ? totalColumns - row * columnsPerRow : columnsPerRow;

            if (isLastRow && columnsInThisRow < columnsPerRow) {
                // 最后一行居中
                const thisRowWidth = columnsInThisRow * COLUMN_WIDTH;
                const thisRowStartX = (config.width - thisRowWidth) / 2 + COLUMN_WIDTH/2;
                x = thisRowStartX + (col * COLUMN_WIDTH);
            } else {
                x = startX + (col * COLUMN_WIDTH);
            }

            const y = startY + (row * rowSpacing);

            const column = new CubeColumn(gameScene, x, y, COLUMN_CAPACITY, cubes);
            columns.push(column);
        });
    }

    function generateRandomLevel(numColumns, numColors, cubesPerColor, emptyColumns) {
        let allCubes = [];
        const availableSoldiers = Phaser.Utils.Array.Shuffle(currentSoldierNames).slice(0, numColors);

        // 生成所有方块 - 每种小兵类型生成4个
        availableSoldiers.forEach(soldierName => {
            for (let i = 0; i < 4; i++) {
                allCubes.push({ soldier: soldierName });
            }
        });

        // 确保参数合理性
        const filledColumns = numColumns - emptyColumns;
        const totalCubes = allCubes.length;

        // 检查是否能合理分配
        if (filledColumns <= 0) {
            console.warn("没有足够的非空列，调整为至少1个非空列");
            emptyColumns = numColumns - 1;
        }

        const maxCubesPerColumn = COLUMN_CAPACITY;
        if (totalCubes > filledColumns * maxCubesPerColumn) {
            console.warn("方块总数超过容量，减少方块数量");
            // 重新计算合理的方块数量
            const reasonableCubesPerColor = Math.floor((filledColumns * maxCubesPerColumn) / numColors);
            allCubes = [];
            availableSoldiers.forEach(soldierName => {
                for (let i = 0; i < reasonableCubesPerColor; i++) {
                    allCubes.push({ soldier: soldierName });
                }
            });
        }

        Phaser.Utils.Array.Shuffle(allCubes);

        const level = [];
        const actualFilledColumns = numColumns - emptyColumns;

        // 尽量均匀分配方块到各列
        let cubeIndex = 0;
        for (let i = 0; i < actualFilledColumns; i++) {
            const columnCubes = [];
            const cubesForThisColumn = Math.ceil((allCubes.length - cubeIndex) / (actualFilledColumns - i));

            for (let j = 0; j < cubesForThisColumn && cubeIndex < allCubes.length; j++) {
                columnCubes.push(allCubes[cubeIndex++]);
            }
            level.push(columnCubes);
        }

        // 添加空列
        for (let i = 0; i < emptyColumns; i++) {
            level.push([]);
        }

        return Phaser.Utils.Array.Shuffle(level);
    }

    // 创建防御塔
    function createDefenseTowers() {
        // 清除现有防御塔
        defenseTowers.forEach(tower => tower.destroy());
        defenseTowers = [];

        // 在屏幕上方创建3个防御塔
        const towerY = 200;
        const towerSpacing = config.width / 4;

        for (let i = 0; i < 3; i++) {
            const towerX = towerSpacing * (i + 1);
            const tower = createDefenseTower(towerX, towerY, i);
            defenseTowers.push(tower);
        }
    }

    // 创建单个防御塔
    function createDefenseTower(x, y, index) {
        const tower = gameScene.add.container(x, y);

        // 塔身
        const towerBody = gameScene.add.graphics();
        towerBody.fillStyle(0x8B4513, 1);
        towerBody.fillRoundedRect(-25, -40, 50, 80, 8);
        towerBody.lineStyle(3, 0x654321);
        towerBody.strokeRoundedRect(-25, -40, 50, 80, 8);

        // 塔顶
        const towerTop = gameScene.add.graphics();
        towerTop.fillStyle(0x696969, 1);
        towerTop.fillRoundedRect(-30, -50, 60, 20, 10);
        towerTop.lineStyle(2, 0x2F4F4F);
        towerTop.strokeRoundedRect(-30, -50, 60, 20, 10);

        // 防御塔emoji
        const towerEmojis = ['🏰', '🗼', '🏯'];
        const towerEmoji = gameScene.add.text(0, -30, towerEmojis[index], {
            fontSize: '32px'
        }).setOrigin(0.5);

        tower.add([towerBody, towerTop, towerEmoji]);

        // 塔的属性 - 强力防御塔，需要大军团才能攻破
        tower.health = 150;
        tower.maxHealth = 150;
        tower.attack = 20;
        tower.attackRange = 250;
        tower.lastAttackTime = 0;
        tower.attackCooldown = 500; // 0.5秒攻击间隔，非常快

        // 血条背景
        const healthBarBg = gameScene.add.graphics();
        healthBarBg.fillStyle(0x000000, 0.5);
        healthBarBg.fillRoundedRect(-25, -70, 50, 8, 4);

        // 血条
        const healthBar = gameScene.add.graphics();
        tower.healthBar = healthBar;
        tower.updateHealthBar = function() {
            healthBar.clear();
            const healthPercent = this.health / this.maxHealth;
            const barColor = healthPercent > 0.5 ? 0x00FF00 : healthPercent > 0.25 ? 0xFFFF00 : 0xFF0000;
            healthBar.fillStyle(barColor, 1);
            healthBar.fillRoundedRect(-25, -70, 50 * healthPercent, 8, 4);
        };

        tower.add([healthBarBg, healthBar]);
        tower.updateHealthBar();

        return tower;
    }

    // 创建战斗单位
    function createBattleUnit(startX, startY, soldierData) {
        const unit = gameScene.add.container(startX, startY);

        // 小兵emoji
        const unitEmoji = gameScene.add.text(0, 0, soldierData.emoji, {
            fontSize: '24px'
        }).setOrigin(0.5);

        unit.add(unitEmoji);

        // 单位属性
        unit.health = soldierData.health;
        unit.maxHealth = soldierData.health;
        unit.attack = soldierData.attack;
        unit.speed = 50; // 移动速度
        unit.target = null; // 攻击目标
        unit.lastAttackTime = 0;
        unit.attackCooldown = 800;

        // 血条背景
        const healthBarBg = gameScene.add.graphics();
        healthBarBg.fillStyle(0x000000, 0.5);
        healthBarBg.fillRoundedRect(-15, -25, 30, 4, 2);

        // 血条
        const healthBar = gameScene.add.graphics();
        unit.healthBar = healthBar;
        unit.updateHealthBar = function() {
            healthBar.clear();
            const healthPercent = this.health / this.maxHealth;
            const barColor = healthPercent > 0.5 ? 0x00FF00 : healthPercent > 0.25 ? 0xFFFF00 : 0xFF0000;
            healthBar.fillStyle(barColor, 1);
            healthBar.fillRoundedRect(-15, -25, 30 * healthPercent, 4, 2);
        };

        unit.add([healthBarBg, healthBar]);
        unit.updateHealthBar();

        // 添加到战斗单位列表
        battleUnits.push(unit);

        // 向上移动到防御塔区域
        gameScene.tweens.add({
            targets: unit,
            y: 250, // 移动到防御塔附近
            duration: 2000,
            ease: 'Power2.easeOut'
        });

        return unit;
    }

    function handleInput(pointer) {
        if (gameWon) return;

        let clickedColumn = null;

        for (let column of columns) {
            if (column.getBounds().contains(pointer.x, pointer.y)) {
                clickedColumn = column;
                break;
            }
        }

        if (clickedColumn) {
            if (selectedColumn === null) {
                if (!clickedColumn.isEmpty()) {
                    selectedColumn = clickedColumn;
                    selectedColumn.highlight();
                }
            } else if (selectedColumn === clickedColumn) {
                selectedColumn.unhighlight();
                selectedColumn = null;
            } else {
                const moveSuccessful = tryMoveCubes(selectedColumn, clickedColumn);
                if (moveSuccessful) {
                    gameScene.time.delayedCall(300, function() {
                        checkWinCondition();
                    });
                }
                selectedColumn.unhighlight();
                selectedColumn = null;
            }
        }
    }

    function tryMoveCubes(sourceColumn, targetColumn) {
        if (sourceColumn.isEmpty()) return false;

        const { cubes: topCubesToMove, soldier: topSoldierToMove } = sourceColumn.getTopCubesInfo();
        const targetTopSoldier = targetColumn.getTopSoldier();
        const targetEmptySpace = targetColumn.getEmptySpace();

        // 检查移动规则 - 只有相同小兵类型可以叠放
        if (!targetColumn.isEmpty() && targetTopSoldier !== topSoldierToMove) {
            return false;
        }

        if (targetEmptySpace < topCubesToMove.length) {
            return false;
        }

        // 执行移动
        sourceColumn.removeCubes(topCubesToMove.length);
        targetColumn.addCubes(topCubesToMove);

        return true;
    }

    function checkWinCondition() {
        // 调试：输出每一列的成语数据
     

        // 首先检查是否有完成的列需要消除
        let hasCompletedColumns = false;
        columns.forEach((column, index) => {
            if (column.isSameSoldierAndFull()) {

                hasCompletedColumns = true;
                // 延迟消除，让玩家看到完成效果
                gameScene.time.delayedCall(1500, function() {
                    eliminateCompletedColumn(column);
                });
            }
        });

        // 如果有完成的列，不立即检查胜利条件
        if (hasCompletedColumns) {
           
            return;
        }

        // 检查是否所有防御塔都被摧毁了
        let allTowersDestroyed = true;
        for (let tower of defenseTowers) {
            if (tower.health > 0 && tower.alpha > 0.1) {
                allTowersDestroyed = false;
                break;
            }
        }

        if (allTowersDestroyed) {
            gameWon = true;

            // 添加胜利粒子效果
            createWinParticles();
            showWinMessage();
        }
    }

    function eliminateCompletedColumn(column) {
        if (!column.isSameSoldierAndFull()) return;

        // 获取小兵类型和数据
        const soldierType = column.cubes[0].soldier;
        const soldierData = currentSoldiers[soldierType];

        // 创建消除动画
        const cubeSprites = [...column.cubeSprites];

        // 添加消除粒子效果
        createEliminationParticles(column);

        // 创建战斗小兵
        cubeSprites.forEach((sprite, index) => {
            gameScene.time.delayedCall(index * 100, function() {
                // 创建向上移动的战斗小兵
                createBattleUnit(sprite.x + column.x, sprite.y + column.y, soldierData);

                // 消除动画
                gameScene.tweens.add({
                    targets: sprite,
                    alpha: 0,
                    scaleX: 1.5,
                    scaleY: 1.5,
                    rotation: Math.PI * 2,
                    duration: 400,
                    ease: 'Back.easeIn',
                    onComplete: () => {
                        sprite.destroy();
                    }
                });
            });
        });

        // 清空列数据
        gameScene.time.delayedCall(cubeSprites.length * 100 + 400, function() {
            column.cubes = [];
            column.cubeSprites = [];
            column.updateCompletionHighlight();

            // 重新检查胜利条件
            checkWinCondition();
        });
    }

    function createEliminationParticles(column) {
        // 在列的位置创建消除粒子效果
        for (let i = 0; i < 30; i++) {
            const particle = gameScene.add.graphics();
            const colors = [0xFFD700, 0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFFFFFF];
            const color = colors[Math.floor(Math.random() * colors.length)];

            particle.fillStyle(color);
            particle.fillCircle(0, 0, Math.random() * 6 + 2);

            particle.x = column.x + (Math.random() - 0.5) * COLUMN_WIDTH;
            particle.y = column.y - COLUMN_HEIGHT/2 + (Math.random() - 0.5) * COLUMN_HEIGHT;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y - Math.random() * 150 - 50,
                x: particle.x + (Math.random() - 0.5) * 100,
                alpha: 0,
                scaleX: 0.2,
                scaleY: 0.2,
                duration: Math.random() * 1000 + 800,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    function createWinParticles() {
        // 创建庆祝粒子效果
        for (let i = 0; i < 50; i++) {
            const particle = gameScene.add.graphics();
            const colors = [0xFFD700, 0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4];
            const color = colors[Math.floor(Math.random() * colors.length)];

            particle.fillStyle(color);
            particle.fillCircle(0, 0, Math.random() * 8 + 4);

            particle.x = Math.random() * config.width;
            particle.y = Math.random() * config.height;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y + Math.random() * 200 + 100,
                x: particle.x + (Math.random() - 0.5) * 200,
                alpha: 0,
                rotation: Math.random() * Math.PI * 2,
                duration: Math.random() * 2000 + 1000,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    function showWinMessage() {
        // 创建胜利背景
        const winBg = gameScene.add.graphics();
        winBg.fillStyle(0x000000, 0.8);
        winBg.fillRect(0, 0, config.width, config.height);

        // 庆祝表情
        const celebrationEmoji = gameScene.add.text(config.width/2, config.height/2 - 120, '🎉', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const winText = gameScene.add.text(config.width/2, config.height/2 - 50, '恭喜过关！', {
            fontSize: '42px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#FFD700',
            stroke: '#000000',
            strokeThickness: 6
        }).setOrigin(0.5);

        const subText = gameScene.add.text(config.width/2, config.height/2 + 10, '所有防御塔都被摧毁了！', {
            fontSize: '20px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'normal',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 检查是否还有下一关
        const hasNextLevel = LEVEL_CONFIG[currentLevel + 1];

        if (hasNextLevel) {
            // 下一关按钮
            const nextLevelBg = gameScene.add.graphics();
            nextLevelBg.fillStyle(0x4CAF50, 0.9);
            nextLevelBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);
            nextLevelBg.lineStyle(3, 0x2E7D32);
            nextLevelBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);

            const nextLevelText = gameScene.add.text(config.width/2, config.height/2 + 85, '下一关', {
                fontSize: '24px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            // 重新开始按钮
            const restartBg = gameScene.add.graphics();
            restartBg.fillStyle(0x2196F3, 0.9);
            restartBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 130, 200, 50, 25);
            restartBg.lineStyle(3, 0x1976D2);
            restartBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 130, 200, 50, 25);

            const restartText = gameScene.add.text(config.width/2, config.height/2 + 155, '重新开始', {
                fontSize: '20px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            // 按钮交互
            nextLevelBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 60, 200, 50), Phaser.Geom.Rectangle.Contains);
            nextLevelBg.on('pointerdown', function() {
                currentLevel++;
                levelText.setText(`第 ${currentLevel} 关`);
                gameWon = false;

               

                // 更新当前关卡的小兵数据
                const levelConfig = LEVEL_CONFIG[currentLevel];
                currentSoldiers = levelConfig.soldiers;
                currentSoldierNames = Object.keys(currentSoldiers);

                // 保存当前关卡到localStorage，然后重新加载页面
                localStorage.setItem('currentLevel', currentLevel.toString());
                location.reload();
            });

            restartBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 130, 200, 50), Phaser.Geom.Rectangle.Contains);
            restartBg.on('pointerdown', function() {
                location.reload();
            });
        } else {
            // 游戏完成
            const completeBg = gameScene.add.graphics();
            completeBg.fillStyle(0xFF9800, 0.9);
            completeBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);
            completeBg.lineStyle(3, 0xF57C00);
            completeBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);

            const completeText = gameScene.add.text(config.width/2, config.height/2 + 85, '游戏完成！', {
                fontSize: '20px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            completeBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 60, 200, 50), Phaser.Geom.Rectangle.Contains);
            completeBg.on('pointerdown', function() {
                location.reload();
            });
        }

        // 动画效果
        gameScene.tweens.add({
            targets: celebrationEmoji,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 600,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        gameScene.tweens.add({
            targets: winText,
            scaleX: 1.05,
            scaleY: 1.05,
            duration: 800,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    // 移除计时器更新函数

    // 移除游戏结束消息函数（因为没有计时器了）


    function update() {
        // 更新所有列的完成状态高亮
        columns.forEach(column => {
            column.updateCompletionHighlight();
        });

        // 更新战斗系统
        updateBattleSystem();
    }

    // 更新战斗系统
    function updateBattleSystem() {
        const currentTime = gameScene.time.now;

        // 防御塔攻击逻辑
        defenseTowers.forEach(tower => {
            if (tower.health <= 0 || tower.alpha <= 0.1) return; // 跳过已摧毁的塔

            // 寻找攻击范围内的敌方单位
            let nearestUnit = null;
            let nearestDistance = tower.attackRange;

            battleUnits.forEach(unit => {
                if (unit.health <= 0) return;

                const distance = Phaser.Math.Distance.Between(tower.x, tower.y, unit.x, unit.y);
                if (distance < nearestDistance) {
                    nearestUnit = unit;
                    nearestDistance = distance;
                }
            });

            // 攻击最近的单位
            if (nearestUnit && currentTime - tower.lastAttackTime > tower.attackCooldown) {
                attackUnit(tower, nearestUnit);
                tower.lastAttackTime = currentTime;
            }
        });

        // 战斗单位攻击逻辑
        battleUnits.forEach(unit => {
            if (unit.health <= 0) return;

            // 清除已摧毁的目标
            if (unit.target && (unit.target.health <= 0 || unit.target.alpha <= 0.1)) {
                unit.target = null;
            }

            // 如果没有目标，寻找最近的存活防御塔
            if (!unit.target) {
                let nearestTower = null;
                let nearestDistance = Infinity;

                defenseTowers.forEach(tower => {
                    if (tower.health <= 0 || tower.alpha <= 0.1) return; // 跳过已摧毁的塔

                    const distance = Phaser.Math.Distance.Between(unit.x, unit.y, tower.x, tower.y);
                    if (distance < nearestDistance) {
                        nearestTower = tower;
                        nearestDistance = distance;
                    }
                });

                unit.target = nearestTower;
            }

            if (unit.target) {
                const distance = Phaser.Math.Distance.Between(unit.x, unit.y, unit.target.x, unit.target.y);

                if (distance <= 100) { // 攻击范围内
                    // 攻击防御塔
                    if (currentTime - unit.lastAttackTime > unit.attackCooldown) {
                        attackTower(unit, unit.target);
                        unit.lastAttackTime = currentTime;
                    }
                } else {
                    // 向目标移动
                    const angle = Phaser.Math.Angle.Between(unit.x, unit.y, unit.target.x, unit.target.y);
                    unit.x += Math.cos(angle) * unit.speed * 0.016;
                    unit.y += Math.sin(angle) * unit.speed * 0.016;
                }
            } else {
                // 没有目标时继续向上移动
                if (unit.y > 150) {
                    unit.y -= unit.speed * 0.016;
                }
            }
        });

        // 清理死亡的单位
        battleUnits = battleUnits.filter(unit => {
            if (unit.health <= 0) {
                unit.destroy();
                return false;
            }
            return true;
        });
    }

    // 攻击单位
    function attackUnit(attacker, target) {
        const damage = attacker.attack;
        target.health -= damage;
        target.updateHealthBar();

        // 攻击者显示白色数字，被攻击者显示红色数字
        showDamageNumber(attacker.x, attacker.y - 30, damage, 0xFFFFFF); // 攻击者白色
        showDamageNumber(target.x, target.y - 30, damage, 0xFF0000);     // 被攻击者红色

        // 创建攻击特效
        createAttackEffect(attacker.x, attacker.y, target.x, target.y);

        if (target.health <= 0) {
            // 单位死亡特效
            createDeathEffect(target.x, target.y);
        }
    }

    // 攻击防御塔
    function attackTower(attacker, target) {
        const damage = attacker.attack;
        target.health -= damage;
        target.updateHealthBar();

        // 攻击者显示白色数字，被攻击者显示红色数字
        showDamageNumber(attacker.x, attacker.y - 30, damage, 0xFFFFFF); // 攻击者白色
        showDamageNumber(target.x, target.y - 60, damage, 0xFF0000);     // 被攻击者红色

        // 创建攻击特效
        createAttackEffect(attacker.x, attacker.y, target.x, target.y);

        if (target.health <= 0) {
            // 防御塔被摧毁特效
            createDeathEffect(target.x, target.y);

            // 防御塔完全消失
            gameScene.tweens.add({
                targets: target,
                alpha: 0,
                scaleX: 0,
                scaleY: 0,
                duration: 500,
                ease: 'Back.easeIn',
                onComplete: () => {
                    // 检查胜利条件
                    checkWinCondition();
                }
            });
        }
    }

    // 创建攻击特效
    function createAttackEffect(fromX, fromY, toX, toY) {
        const line = gameScene.add.graphics();
        line.lineStyle(3, 0xFFFF00, 1);
        line.lineBetween(fromX, fromY, toX, toY);

        // 闪烁效果
        gameScene.tweens.add({
            targets: line,
            alpha: 0,
            duration: 200,
            onComplete: () => line.destroy()
        });
    }

    // 创建死亡特效
    function createDeathEffect(x, y) {
        for (let i = 0; i < 10; i++) {
            const particle = gameScene.add.graphics();
            particle.fillStyle(0xFF0000);
            particle.fillCircle(0, 0, Math.random() * 4 + 2);
            particle.x = x + (Math.random() - 0.5) * 20;
            particle.y = y + (Math.random() - 0.5) * 20;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y - Math.random() * 50 - 20,
                x: particle.x + (Math.random() - 0.5) * 40,
                alpha: 0,
                duration: Math.random() * 800 + 400,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    // 显示伤害数字
    function showDamageNumber(x, y, damage, color) {
        const damageText = gameScene.add.text(x, y, `-${damage}`, {
            fontSize: '20px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: `#${color.toString(16).padStart(6, '0')}`,
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 伤害数字动画
        gameScene.tweens.add({
            targets: damageText,
            y: y - 40,
            alpha: 0,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 800,
            ease: 'Power2.easeOut',
            onComplete: () => damageText.destroy()
        });
    }

    // CubeColumn 类 - 方块列容器
    class CubeColumn extends Phaser.GameObjects.Container {
        constructor(scene, x, y, capacity, initialCubes) {
            super(scene, x, y);
            scene.add.existing(this);

            this.capacity = capacity;
            this.cubes = initialCubes || [];
            this.cubeSprites = [];

            // 绘制列的底座
            this.drawBase();

            // 创建方块精灵
            this.updateCubeVisuals();

            // 设置交互区域
            this.setSize(COLUMN_WIDTH, COLUMN_HEIGHT);
            this.setInteractive();
        }

        drawBase() {
            // 绘制列的底座和边框 - 竖屏优化
            this.baseGraphics = this.scene.add.graphics();

         

            // 边框指示器 - 适合竖屏的视觉效果
            this.baseGraphics.lineStyle(3, 0x607d8b, 0.8);
            this.baseGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 6, -COLUMN_HEIGHT + 35, COLUMN_WIDTH - 12, COLUMN_HEIGHT - 35, 6);

            // 内部虚线指示器
            this.baseGraphics.lineStyle(2, 0x90a4ae, 0.4);
            for (let i = 1; i < this.capacity; i++) {
                const y = -i * CUBE_SIZE;
                this.baseGraphics.lineBetween(-COLUMN_WIDTH/2 + 10, y, COLUMN_WIDTH/2 - 10, y);
            }

            this.add(this.baseGraphics);
        }

        updateCubeVisuals() {
            // 清除现有方块精灵
            this.cubeSprites.forEach(sprite => sprite.destroy());
            this.cubeSprites = [];

            // 从底部向上创建方块精灵
            for (let i = 0; i < this.cubes.length; i++) {
                const cube = this.cubes[i];
                const soldier = currentSoldiers[cube.soldier];
                const colors = soldier.colors;

                // 创建方块容器
                const cubeContainer = this.scene.add.container(0, -i * CUBE_SIZE - CUBE_SIZE/2);

                // 创建方块图形
                const graphics = this.scene.add.graphics();

                // 绘制3D效果的方块
                graphics.fillStyle(colors.main);
                graphics.fillRoundedRect(-CUBE_SIZE/2, -CUBE_SIZE/2, CUBE_SIZE, CUBE_SIZE, 8);

                graphics.fillStyle(colors.shadow);
                graphics.fillRoundedRect(-CUBE_SIZE/2 + 4, -CUBE_SIZE/2 + 4, CUBE_SIZE-8, CUBE_SIZE-8, 6);

                graphics.fillStyle(colors.highlight);
                graphics.fillRoundedRect(-CUBE_SIZE/2 + 8, -CUBE_SIZE/2 + 8, CUBE_SIZE-16, CUBE_SIZE-16, 4);

                graphics.fillStyle(colors.main);
                graphics.fillRoundedRect(-CUBE_SIZE/2 + 12, -CUBE_SIZE/2 + 12, CUBE_SIZE-24, CUBE_SIZE-24, 3);

                // 创建emoji文本
                const text = this.scene.add.text(0, 0, soldier.emoji, {
                    fontSize: '32px'
                }).setOrigin(0.5);

                // 将图形和文本添加到容器
                cubeContainer.add([graphics, text]);

                // 添加轻微的随机旋转和缩放效果 - 竖屏优化
                const randomRotation = (Math.random() - 0.5) * 0.06;
                const randomScale = 0.97 + Math.random() * 0.06;
                cubeContainer.setRotation(randomRotation);
                cubeContainer.setScale(randomScale);

                // 添加深度效果
                const depthOffset = (Math.random() - 0.5) * 3;
                cubeContainer.x = depthOffset;

                this.add(cubeContainer);
                this.cubeSprites.push(cubeContainer);
            }

            // 更新完成状态高亮
            this.updateCompletionHighlight();
        }

        getTopSoldier() {
            if (this.cubes.length === 0) return null;
            return this.cubes[this.cubes.length - 1].soldier;
        }

        getTopCubesInfo() {
            if (this.cubes.length === 0) return { cubes: [], soldier: null };

            const topSoldier = this.getTopSoldier();
            let count = 0;
            for (let i = this.cubes.length - 1; i >= 0; i--) {
                if (this.cubes[i].soldier === topSoldier) {
                    count++;
                } else {
                    break;
                }
            }
            return {
                cubes: this.cubes.slice(this.cubes.length - count),
                soldier: topSoldier
            };
        }

        getEmptySpace() {
            return this.capacity - this.cubes.length;
        }

        isEmpty() {
            return this.cubes.length === 0;
        }

        addCubes(cubesToAdd) {
            this.cubes = this.cubes.concat(cubesToAdd);
            this.updateCubeVisuals();

            // 添加方块掉落动画 - 竖屏优化
            const newCubes = this.cubeSprites.slice(-cubesToAdd.length);
            newCubes.forEach((sprite, index) => {
                sprite.y -= 100; // 从更高处开始
                sprite.alpha = 0.7;
                sprite.scaleX = 0.8;
                sprite.scaleY = 0.8;

                this.scene.tweens.add({
                    targets: sprite,
                    y: -(this.cubes.length - cubesToAdd.length + index) * CUBE_SIZE - CUBE_SIZE/2,
                    alpha: 1,
                    scaleX: sprite.scaleX / 0.8,
                    scaleY: sprite.scaleY / 0.8,
                    duration: 280,
                    ease: 'Back.easeOut',
                    delay: index * 40, // 错开动画时间
                    onComplete: () => {
                        // 动画完成后更新完成状态高亮
                        this.updateCompletionHighlight();
                    }
                });
            });
        }

        removeCubes(count) {
            // 添加移除动画 - 竖屏优化
            const cubesToRemove = this.cubeSprites.slice(-count);
            cubesToRemove.forEach((sprite, index) => {
                this.scene.tweens.add({
                    targets: sprite,
                    alpha: 0,
                    y: sprite.y - 70,
                    scaleX: 0.6,
                    scaleY: 0.6,
                    rotation: (Math.random() - 0.5) * 0.4,
                    duration: 230,
                    delay: index * 25,
                    ease: 'Power2.easeIn',
                    onComplete: () => {
                        sprite.destroy();
                        // 最后一个动画完成后更新完成状态高亮
                        if (index === cubesToRemove.length - 1) {
                            this.updateCompletionHighlight();
                        }
                    }
                });
            });

            this.cubes.splice(this.cubes.length - count, count);
            this.cubeSprites.splice(-count, count);
        }

        isSameSoldierAndFull() {
            if (this.cubes.length === 0) return false;
            if (this.cubes.length < this.capacity) return false;

            const firstSoldier = this.cubes[0].soldier;
            // 检查是否都是同一种小兵类型
            return this.cubes.every(cube => cube.soldier === firstSoldier);
        }

        // 检查是否整列都是同一种小兵类型
        isSameSoldierColumn() {
            if (this.cubes.length === 0) return false;
            const firstSoldier = this.cubes[0].soldier;
            return this.cubes.every(cube => cube.soldier === firstSoldier);
        }



        highlight() {
            // 添加高亮效果 - 竖屏优化
            if (this.highlightGraphics) {
                this.highlightGraphics.destroy();
            }

            this.highlightGraphics = this.scene.add.graphics();

            // 外层高亮边框
            this.highlightGraphics.lineStyle(6, 0xFFD700, 1);
            this.highlightGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 3, -COLUMN_HEIGHT + 35, COLUMN_WIDTH - 6, COLUMN_HEIGHT - 35, 6);

            // 内层发光效果
            this.highlightGraphics.lineStyle(3, 0xFFFFFF, 0.6);
            this.highlightGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 6, -COLUMN_HEIGHT + 38, COLUMN_WIDTH - 12, COLUMN_HEIGHT - 38, 4);

            this.add(this.highlightGraphics);

            // 添加脉冲效果
            this.scene.tweens.add({
                targets: this.highlightGraphics,
                alpha: 0.4,
                scaleX: 1.04,
                scaleY: 1.02,
                duration: 380,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }

        unhighlight() {
            if (this.highlightGraphics) {
                this.highlightGraphics.destroy();
                this.highlightGraphics = null;
            }
        }

        // 更新完成状态高亮
        updateCompletionHighlight() {
            // 清除现有的完成高亮
            if (this.completionHighlight) {
                this.completionHighlight.destroy();
                this.completionHighlight = null;
            }

            // 如果列已完成，添加特殊高亮效果
            if (this.isSameSoldierAndFull()) {
                this.completionHighlight = this.scene.add.graphics();

                // 获取小兵对应的颜色
                const soldier = currentSoldiers[this.cubes[0].soldier];
                const colors = soldier.colors;

                // 绘制完成状态的特殊边框
                this.completionHighlight.lineStyle(8, 0x00FF00, 0.9); // 绿色外边框
                this.completionHighlight.strokeRoundedRect(-COLUMN_WIDTH/2 + 1, -COLUMN_HEIGHT + 33, COLUMN_WIDTH - 2, COLUMN_HEIGHT - 33, 8);

                // 内层金色边框
                this.completionHighlight.lineStyle(4, 0xFFD700, 0.8);
                this.completionHighlight.strokeRoundedRect(-COLUMN_WIDTH/2 + 5, -COLUMN_HEIGHT + 37, COLUMN_WIDTH - 10, COLUMN_HEIGHT - 37, 6);

                // 添加发光效果
                this.completionHighlight.lineStyle(2, 0xFFFFFF, 0.6);
                this.completionHighlight.strokeRoundedRect(-COLUMN_WIDTH/2 + 9, -COLUMN_HEIGHT + 41, COLUMN_WIDTH - 18, COLUMN_HEIGHT - 41, 4);

                this.add(this.completionHighlight);

                // 添加闪烁动画
                this.scene.tweens.add({
                    targets: this.completionHighlight,
                    alpha: 0.3,
                    duration: 800,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });

                // 添加缩放脉冲效果
                this.scene.tweens.add({
                    targets: this.completionHighlight,
                    scaleX: 1.02,
                    scaleY: 1.01,
                    duration: 1200,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });
            }
        }

        getBounds() {
            return new Phaser.Geom.Rectangle(
                this.x - COLUMN_WIDTH/2,
                this.y - COLUMN_HEIGHT,
                COLUMN_WIDTH,
                COLUMN_HEIGHT
            );
        }
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
